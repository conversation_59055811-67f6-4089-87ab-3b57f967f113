version: '3.8'

services:
  wullup-website:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: wullup-website-dev
    ports:
      - "4321:4321"
    volumes:
      # Mount source code for hot reloading
      - .:/app
      # Exclude node_modules to avoid conflicts
      - /app/node_modules
      # Exclude build output
      - /app/dist
      - /app/.astro
    environment:
      - NODE_ENV=development
      - HOST=0.0.0.0
      - PORT=4321
      # Load environment variables from .env file
    env_file:
      - .env
    networks:
      - wullup-network
    restart: unless-stopped
    # Enable development features
    stdin_open: true
    tty: true
    # Health check to ensure the service is running
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:4321"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  wullup-network:
    driver: bridge

# Optional: Add volumes for persistent data if needed in the future
volumes:
  node_modules:
